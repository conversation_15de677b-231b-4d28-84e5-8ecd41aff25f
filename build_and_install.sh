#!/bin/bash

# Скрипт для сборки и установки uProd с автоматическим копированием звуков

echo "🚀 Начинаем сборку uProd..."

# Очищаем предыдущие сборки
echo "🧹 Очищаем предыдущие сборки..."
rm -rf build

# Собираем проект
echo "🔨 Собираем проект..."
xcodebuild -project SimplePomodoroTest.xcodeproj -scheme SimplePomodoroTest -configuration Release -derivedDataPath ./build

if [ $? -ne 0 ]; then
    echo "❌ Ошибка при сборке проекта!"
    exit 1
fi

echo "✅ Сборка завершена успешно!"

# Копируем звуки с правильной структурой
echo "🎵 Копируем звуковые файлы..."
mkdir -p "build/Build/Products/Release/uProd.app/Contents/Resources/Sounds"
cp -R "Sounds/" "build/Build/Products/Release/uProd.app/Contents/Resources/Sounds/"

# Проверяем, что звуки скопировались
echo "🔍 Проверяем звуковые файлы:"
find "build/Build/Products/Release/uProd.app/Contents/Resources/Sounds" -name "*.mp3"

# Удаляем старую версию из Applications
echo "🗑️ Удаляем старую версию из Applications..."
rm -rf "/Applications/uProd.app"

# Копируем новую версию в Applications
echo "📦 Устанавливаем новую версию в Applications..."
cp -r "build/Build/Products/Release/uProd.app" "/Applications/"

# Проверяем установку
if [ -d "/Applications/uProd.app" ]; then
    echo "✅ uProd успешно установлен в Applications!"
    echo "🎵 Проверяем звуковые файлы в установленном приложении:"
    find "/Applications/uProd.app/Contents/Resources/Sounds" -name "*.mp3"
    echo ""
    echo "🎉 Готово! Теперь вы можете запустить uProd из папки Applications."
    echo "🔊 Звуки должны работать корректно!"
else
    echo "❌ Ошибка при установке приложения!"
    exit 1
fi

# Очищаем временные файлы
echo "🧹 Очищаем временные файлы..."
rm -rf build

echo "✨ Все готово!"
